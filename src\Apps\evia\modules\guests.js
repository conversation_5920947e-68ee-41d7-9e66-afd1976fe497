import { numberFormat } from "../../../Utils/functions";

export default {
  name: "Guests",
  icon: "UsergroupAddOutlined",
  path: "/guests",
  collection: "guests",
  singular: "Guest",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
    },
    {
      title: "Name",
      hideInForm: true,
      render: function render(_, record) {
        return (
          (record.title === "None" ? "" : record.title + ". ") +
          record.sur_name +
          " " +
          record.first_name
        );
      },
    },
    {
      title: "Title",
      dataIndex: "title",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      width: "lg",
      colProps: {
        xs: 4,
        md: 8,
      },
      valueEnum: {
        Hon: {
          text: "Hon",
        },
        Dr: {
          text: "Dr",
        },
        Mr: {
          text: "Mr",
        },
        Mrs: {
          text: "Mrs",
        },
        Ms: {
          text: "Ms",
        },
        Miss: {
          text: "Miss",
        },

        None: {
          text: "None",
        },
      },
    },
    {
      title: "Sur Name",
      dataIndex: "sur_name",
      sorter: true,
      hideInTable: true,
      isRequired: true,
      isPrintable: true,
      width: "lg",
      colProps: {
        xs: 10,
        md: 8,
      },
    },
    {
      title: "First Name",
      dataIndex: "first_name",
      sorter: true,
      hideInTable: true,
      isPrintable: true,
      width: "lg",
      colProps: {
        xs: 10,
        md: 8,
      },
    },
    {
      title: "Phone",
      dataIndex: "phone",
      sorter: true,
      copyable: true,
      isPrintable: true,
      type: "phone",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      valueType: "email",
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },

    {
      title: "Mobile",
      dataIndex: "mobile",
      sorter: true,
      type: "phone",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Address",
      dataIndex: "address",
      sorter: true,
      valueType: "text",
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    // {
    //   title: "Balance",
    //   dataIndex: "balance",
    //   hideInForm: true,
    //   render: (
    //     v,
    //     { check_in_bill, check_in_receipts, events_balance, order_balance }
    //   ) =>
    //     numberFormat(
    //       check_in_bill - check_in_receipts + events_balance + order_balance
    //     ),
    // },
  ],
};
