import React from "react";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message } from "antd";
import PouchDb from "pouchdb-browser";

export default {
  MoreActions: (props) => {
    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    const menus = [];

    

    record &&
      record.room_status === "Mess" &&
      menus.push({
        key: "Housekeeping",
        name: "Housekeeping",
        onClick: () => {
          if (record) {
            return pouchDatabase(
              modules.housekeeping.collection,
              databasePrefix
            )
              .saveDocument(
                {
                  room_number: {
                    value: record._id,
                    label: `${record.number} ${record.short_desc}`,
                  },
                  cleaner: CRUD_USER,
                  description: "Housekeeping",
                },
                CRUD_USER
              )
              .then(async () => {
                const rooms = await pouchDatabase(
                  modules.rooms.collection,
                  databasePrefix
                ).saveDocument({
                  ...record,
                  room_status: "Vacant",
                });
              })
              .then(() => {
                message.success(`Room ${record.number} Cleaned.`);

                return true;
              });
          }
        },

        // name: (
        //   <>
        //     <BetaSchemaForm
        //       submitter={{
        //         searchConfig: {
        //           resetText: "Close",
        //           submitText: "Save",
        //         },
        //       }}
        //       modalProps={{ centered: true }}
        //       grid={true}
        //       trigger={
        //         <a key="button" type="primary">
        //           Housekeeping
        //         </a>
        //       }
        //       title={"Housekeeping"}
        //       destroyOnClose={true}
        //       layoutType="ModalForm"
        //       initialValues={{
        //         order: {
        //           label: record._id,
        //           ...record,
        //         },
        //       }}
        //       onFinish={async (values) => {
        //         if (record) {
        //           return pouchDatabase(
        //             modules.housekeeping.collection,
        //             databasePrefix
        //           )
        //             .saveDocument({ ...values }, CRUD_USER)
        //             .then(() => {
        //               message.success(
        //                 `${record.guest && record.guest.label} Room Cleaned.`
        //               );

        //               return true;
        //             });
        //         }
        //       }}
        //       columns={[...modules.housekeeping.columns]}
        //     />
        //   </>
        // ),
      });

    return <TableDropdown key="actionGroup" menus={menus}></TableDropdown>;
  },
};
