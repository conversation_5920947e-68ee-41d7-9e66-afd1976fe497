export default {
  name: "Manager's Reports",
  icon: "TableOutlined",
  path: "manager_reports",
  collection: "manager_reports",
  singular: "Manager's Report",
  columns: [
    {
      title: "Time",
      dataIndex: "time",
      valueType: "time",
    },
    {
      title: "Stuff at counter",
      dataIndex: "stuff_at_counter",
      type: "dbSelect",
      collection: "users",
      label: ["surname", "firstname"],
    },
    {
      title: "supervisor",
      dataIndex: "supervisor",
      type: "dbSelect",
      collection: "users",
      label: ["surname", "firstname"],
    },
    {
      title: "Frontdesk is organized",
      dataIndex: "frontdesk_is_organized",
      valueType: "switch",
      fieldProps: {
        checkedChildren: "Yes",
        unCheckedChildren: "No",
      },
    },
    {
      title: "Stuff groomed well with name and badge",
      dataIndex: "stuff_grooming",
      valueType: "switch",
      fieldProps: {
        checkedChildren: "Yes",
        unCheckedChildren: "No",
      },
    },
  ],
};
