import moment from "moment";
import checkins from "./checkins";
import invoices from "./invoices";
import { formatMoney, numberFormat } from "../../../Utils/functions";
export default {
  name: "Hotel Receipt",
  icon: "CreditCardOutlined",
  path: "hotel/receipts",
  parent: "hotel",
  collection: "receipts",
  singular: "Receipt",
  removeCreate: true,
  columns: [
    {
      title: "Payment Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
      initialValue: moment().startOf("day"),
    },
    {
      type: "dbSelect",
      isRequired: true,
      dataIndex: "checkin",
      collection: checkins.collection,
      title: "Check In",
      isPrintable: true,
      label: ["guest.label", "-", "_id"],
      width: "lg",
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isPrintable: true,
      isRequired: true,
      width: "lg",
      colProps: {
        md: 12,
      },
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        "Mobile Money": {
          text: "Mobile Money",
        },
      },
    },
    {
      title: "Amount",
      dataIndex: "amount",
      isPrintable: true,
      isRequired: true,
      type: "digit",
    },
    {
      valueType: "dependency",
      hideInTable: true,
      title: "Total",
      width: "lg",
      fieldProps: {
        name: ["checkin", "currency", "amount"],
      },
      columns: ({ checkin = null, currency, amount = 0 }) => {
        const items = checkin && checkin.items,
          room_rate = checkin && checkin.room_rate,
          arrival_date = checkin && checkin.arrival_date,
          departure_date = checkin && checkin.departure_date;
        var total = 0,
          rental_charges = 0,
          extensions = 0,
          otherServices = 0,
          previousReceipts = 0;

        if (checkin && checkin.extensions) {
          total = total + checkin.extensions;
          extensions = checkin.extensions;
        }

        

        if (checkin && checkin.receipts) {
          total = total - checkin.receipts;
          previousReceipts = checkin.receipts;
        }

        if (room_rate) {
          total =
            total +
            (moment(departure_date)
              .startOf("day")
              .diff(moment(arrival_date).startOf("day"), "days") < 1
              ? 1
              : moment(departure_date)
                  .startOf("day")
                  .diff(moment(arrival_date).startOf("day"), "days")) *
              room_rate;
          rental_charges =
            (moment(departure_date)
              .startOf("day")
              .diff(moment(arrival_date).startOf("day"), "days") < 1
              ? 1
              : moment(departure_date)
                  .startOf("day")
                  .diff(moment(arrival_date).startOf("day"), "days")) *
            room_rate;
        }

        items &&
          items.map(function (item) {
            total = item
              ? total +
                ((item.tax ? item.tax : 0) / 100) *
                  ((item.cost ? item.cost : 0) *
                    (item.quantity ? item.quantity : 0)) +
                (item.cost ? item.cost : 0) *
                  (item.quantity ? item.quantity : 0)
              : total;
            otherServices = item
              ? (otherServices +=
                  ((item.tax ? item.tax : 0) / 100) *
                    ((item.cost ? item.cost : 0) *
                      (item.quantity ? item.quantity : 0)) +
                  (item.cost ? item.cost : 0) *
                    (item.quantity ? item.quantity : 0))
              : otherServices;
          });

        total = total - amount;
        return [
          {
            dataIndex: "balance",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return numberFormat(v);
            },
            renderFormItem: function renderFormItem() {
              return (
                "Rental Charges : " +
                numberFormat(rental_charges) +
                " | Other Bills :  " +
                numberFormat(otherServices) +
                " | Extensions :  " +
                numberFormat(extensions) +
                " | Previous Receipts :  (" +
                numberFormat(previousReceipts) +
                ") | Balance :  " +
                numberFormat(total) +
                " "
              );
            },
          },
        ];
      },
    },
  ],
};
