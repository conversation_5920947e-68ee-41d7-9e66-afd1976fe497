import { formatMoney, numberFormat } from "../../../Utils/functions";
export default {
  name: "Orders",
  icon: "ShoppingCartOutlined",
  path: "orders",
  collection: "orders",
  singular: "Order",
  columns: [
    {
      type: "dbSelect",
      collection: "guests",
      label: ["sur_name", "first_name"],
      dataIndex: "guest",
      title: "Guest",
      width: "lg",
      valueType: "text",
      isRequired: true,
      filters: true,
      onFilter: true,
      isPrintable: true,
    },
    {
      title: "Table",
      dataIndex: "table",
      type: "dbSelect",
      collection: "tables",
      label: ["tableName"],
      isPrintable: true,
    },

    {
      title: "Items",
      valueType: "formList",
      width: "lg",
      colProps: {
        md: 24,
      },
      // hideInTable: true,
      fieldprops: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      dataIndex: "items",
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              dataIndex: "item",
              title: "Menu Item",
              type: "dbSelect",
              valueType: "select",
              collection: "menus",
              label: ["itemName", " - ", "price"],
              colProps: {
                md: 8,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Please Select an Item`,
                  },
                ],
              },
            },
            {
              title: "Qty",
              dataIndex: "quantity",
              valueType: "digit",
              colProps: {
                md: 4,
              },
            },
            {
              title: "Unit Cost",
              colProps: {
                md: 6,
              },
              dataIndex: "cost",
              valueType: "digit",
            },

            // {
            //   valueType: "dependency",
            //   hideInTable: true,
            //   sorter: true,
            //   fiiters: true,
            //   fieldProps: {
            //     name: ["item"],
            //   },
            //   columns: ({ item }) => {
            //     let columns = [];

            //     if (item && item.label.split(" - ")[1]) {
            //       columns.push({
            //         title: "Unit Cost",
            //         colProps: {
            //           md: 6,
            //         },
            //         dataIndex: "cost",
            //         valueType: "digit",
            //         initialValue: item && item.label.split(" - ")[1],
            //       });
            //     }

            //     return columns;
            //   },
            // },

            {
              valueType: "dependency",
              fieldProps: {
                name: ["cost", "quantity"],
              },
              columns: ({ cost, quantity }) => {
                return [
                  {
                    title: "Sub Total",
                    dataIndex: "subTotal",
                    colProps: {
                      md: 5,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (cost ? cost : 0) * (quantity ? quantity : 0),
                    },
                  },
                ];
              },
            },
          ],
        },
      ],
      render: (record, { items }) => {
        let allItems = "";
        items &&
          items.map(
            (item, index) =>
              (allItems = item
                ? allItems +
                  item.item.label.split(" - ")[0] +
                  (index + 1 === items.length ? "" : ", ")
                : allItems)
          );
        return allItems;
      },
    },
    {
      title: "Fully Paid",
      dataIndex: "fullyPaid",
      valueType: "switch",
      fieldProps: {
        unCheckedChildren: "No",
        checkedChildren: "Yes",
      },
      colProps: {
        md: 4,
        offset: 8,
      },
    },
    {
      title: "Waiter",
      dataIndex: "waiter",
      type: "dbSelect",
      isPrintable: true,
      collection: "users",
      label: ["first_name", "last_name"],
      colProps: {
        md: 6,
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      name: ["items"],
      columns: ({ items }) => {
        let columnsToRender = [
          {
            dataIndex: "total",
            title: "Total",
            colProps: {
              md: 6,
            },
            fieldProps: {
              disabled: true,
              value:
                items &&
                items.length > 0 &&
                items.reduce(
                  (pv, cv) =>
                    pv +
                    (cv.cost ? cv.cost : 0) * (cv.quantity ? cv.quantity : 0),
                  0
                ),
            },
          },
        ];

        return columnsToRender;
      },
    },
    {
      title: "Total",
      dataIndex: "total",
      valueType: "digit",
      hideInForm: true,
      isPrintable: true,
      render: function render(v, record) {
        

        const balance = record.items
          ? record.items.reduce(
              (total, item) =>
                total +
                (item.cost ? item.cost : 0) *
                  (item.quantity ? item.quantity : 0),
              0
            )
          : 0;

        return numberFormat(balance);
      },
    },
    {
      title: "Balance",
      dataIndex: "balance",
      valueType: "digit",
      hideInForm: true,
      isPrintable: true,
      render: function render(v, record) {
        

        const balance = record.items
          ? record.items.reduce(
              (total, item) =>
                total +
                (item.cost ? item.cost : 0) *
                  (item.quantity ? item.quantity : 0),
              0
            )
          : 0;

        return numberFormat(balance - record.receipts);
      },
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 24,
      },
    },
    // {
    //   title: "Order Status",
    //   dataIndex: "orderStatus",
    //   valueType: "select",
    //   isPrintable: true,
    //   filters: true,
    //   valueEnum: {
    //     Pending: {
    //       text: "Pending",
    //     },
    //     Confirmed: {
    //       text: "Confirmed",
    //     },
    //     Cancelled: {
    //       text: "Cancelled",
    //     },
    //   },
    // },
  ],
};
