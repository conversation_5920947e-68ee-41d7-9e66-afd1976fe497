import PouchDb from "pouchdb-browser";

export default {
  buffResults: async (results) => {
    const stockDB = new PouchDb("stock_purchasing");
    const invoicesDB = new PouchDb("orders");

    const dbData = await Promise.all([
      (
        await stockDB.allDocs({ include_docs: true, binary: true })
      ).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map((r) => r.doc),
      (
        await invoicesDB.allDocs({ include_docs: true, binary: true })
      ).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map((r) => r.doc),
    ]);

    const [stock, invoices] = dbData;

    let allSold = [];

    invoices.forEach((element) => {
      allSold.push(...element.items.map((e) => ({ ...e, date: element.date })));
    });

    return results.map((product) => {
      const soldProducts = allSold.filter(
        (element) => element.item.value === product._id
      );

      product.sold_list = soldProducts;
      if (soldProducts.length > 0) {
        product.sold = soldProducts.reduce(
          (pv, cv) => pv + Number(cv.quantity),
          0
        );
      } else {
        product.sold = 0;
      }

      let stockRecords = [];

      stock.forEach((invoice, index) => {
        if (invoice.items) {
          invoice.items.forEach((item) =>
            stockRecords.push({
              date: invoice.date,
              ...item,
            })
          );
        } else {
          stockRecords.push({ price: invoice.unit_cost, ...invoice });
        }
      });

      const newStock = stockRecords.filter(
        (doc) => doc.product.value === product._id
      );
      product.stock_list = newStock;
      if (newStock.length > 0) {
        product.stoked = newStock.reduce((pv, cv) => pv + cv.quantity, 0);
      } else {
        product.stoked = 0;
      }

      return product;
    });
  },
};
