
import guests from "./guests";
import invoices from "./invoices";
import general_receipts from "./general_receipts";
import orders from "./orders";
import menus from "./menus";
import tables from "./tables";
import menu_categories from "./menu_categories";
import order_receipts from "./order_receipts";
import { suppliers } from "./suppliers";
import { expenses } from "./expenses";
import { expense_categories } from "./expense_categories";
import { stock_purchasing } from "./stock_purchasing";
import stock_payments from "./stock_payments";
export default {
  guests: guests,
  orders: orders,
  order_receipts: order_receipts,
  menu_management: {
    name: "Menu Management",
    description: "Menu Management",
    icon: "DollarOutlined",
    path: "/menu_management",
    columns: [],
  },
  menus: menus,
  tables: tables,
  menu_categories: menu_categories,
  expenditure: {
    name: "Expenditure",
    description: "Expenditure",
    icon: "DollarOutlined",
    path: "/expenditure",
    columns: [],
  },
  expenses: expenses,
  suppliers: suppliers, 
  expense_categories: expense_categories,
  stock_payments: stock_payments,
  stock_purchasing: stock_purchasing,
};
