import { numberFormat } from "../../../Utils/functions";

export default {
  name: "Order Receipts",
  icon: "CreditCardOutlined",
  path: "receipts",
  collection: "order_receipts",
  singular: "Order Receipt",
  removeCreate: true,
  columns: [
    {
      title: "Order",
      dataIndex: "order",
      type: "dbSelect",
      collection: "orders",
      label: ["guest.label", "-", "_id"],
      isPrintable: true,
    },
    {
      title: "Payment Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      isPrintable: true,
      initialValue: "Cash",
      width: "lg",
      valueEnum: {
        Cash: {
          text: "Cash",
        },

        "Mobile Money": {
          text: "Mobile Money",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        Bank: {
          text: "Bank",
        },
      },
    },
    {
      title: "Amount Paid",
      dataIndex: "amount",
      isRequired: true,
      valueType: "digit",
      isPrintable: true,
    },
    {
      title: "Description",
      dataIndex: "description",
      type: "textarea",
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 24,
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      title: "Total",
      width: "lg",
      fieldProps: {
        name: ["order", "amount"],
      },
      columns: ({ order = null, amount = 0 }) => {
        

        let resources_charges = 0,
          previous_receipts = 0,
          total = 0;

        resources_charges = order.items.reduce(
          (pv, cv) => pv + cv.cost * cv.quantity,
          0
        );

        previous_receipts = order.receipts;

        total = resources_charges - previous_receipts - amount;

        return [
          {
            dataIndex: "balance",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return numberFormat(v);
            },

            renderFormItem: function renderFormItem() {
              return (
                "Order Bill :  " +
                numberFormat(resources_charges) +
                " | Previous Receipts :  (" +
                numberFormat(previous_receipts) +
                ") | Balance :  " +
                numberFormat(total) +
                " "
              );
            },
          },
        ];
      },
    },
  ],
};
