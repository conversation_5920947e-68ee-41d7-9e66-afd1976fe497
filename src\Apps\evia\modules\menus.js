import { numberFormat } from "../../../Utils/functions";

export default {
  name: "Menu Items",
  icon: "MenuOutlined",
  path: "/menu_management/menus",
  parent: "menu_management",
  collection: "menus",
  singular: "Menu",
  multi_Branch: true,
  columns: [
    {
      title: "Item Name",
      dataIndex: "itemName",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Category",
      dataIndex: "category",
      type: "dbSelect",
      collection: "menu_categories",
      label: ["name"],
      isPrintable: true,
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "text",
      hideInTable: true,
      isPrintable: true,
    },
    {
      title: "Price",
      dataIndex: "price",
      valueType: "digit",
      isRequired: true,
      isPrintable: true,
    },
    {
      valueType: "digit",
      dataIndex: "stock_in",
      title: "Stock In",
      isPrintable: true,
      hideInForm: true,
      render: (text, record) => {
        return record.stockable
          ? numberFormat(
              (record.stoked ? record.stoked : 0) -
                (record.sold ? record.sold : 0)
            )
          : "-";
      },
    },
    {
      title: "Stockable",
      dataIndex: "stockable",
      valueType: "switch",
      hideInTable: true,
    },
  ],
};
