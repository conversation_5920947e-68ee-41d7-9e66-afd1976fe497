import moment from "moment";

export default {
  name: "Reservations",
  icon: "HomeOutlined",
  path: "hotel/reservations",
  collection: "reservations",
  singular: "Reservation",
  parent: "hotel",
  columns: [
    {
      type: "dbSelect",
      collection: "guests",
      label: ["title", "sur_name", "first_name"],
      dataIndex: "guest",
      title: "Guest",
      width: "lg",
    },
    // {
    //   type: 'dbSelect',
    //   collection: 'rooms',
    //   label: ["number", 'short_desc'],
    //   dataIndex: 'room',
    //   title: 'Room',
    //   width: 'lg'
    // },
    {
      title: "Expected Check in Date",
      dataIndex: "check_in",
      sorter: true,
      valueType: "date",
    },
    {
      title: "Expected Check out Date",
      dataIndex: "check_out",
      sorter: true,
      valueType: "date",
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: ["check_in", "check_out"],
      columns: function columns(_ref) {
        var check_in = _ref.check_in,
          check_out = _ref.check_out;
        return [
          {
            dataIndex: "stay_duration",
            title: "Expected Duration Of Stay In Uganda",
            valueType: "digits",
            colProps: {
              md: 12,
            },
            fieldProps: {
              addonAfter: "Nights",
              style: {
                width: "100%",
              },
              disabled: true,
              value:
                check_out && check_in
                  ? moment(check_out).diff(moment(check_in), "days")
                  : 0,
            },
          },
        ];
      },
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        Airline: {
          text: "Airline",
        },
        EFT: {
          text: "EFT",
        },
        "Travelers Cheque": {
          text: "Travelers Cheque",
        },
        "Travel Agent": {
          text: "Travel Agent",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Company: {
          text: "Company",
        },
      },
    },
    {
      title: "Rate",
      dataIndex: "rate",
      valueType: "digit",
      hideInTable: true,
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: ["rate", "check_in", "check_out"],
      columns: function columns(_ref3) {
        var rate = _ref3.rate,
          check_in = _ref3.check_in,
          check_out = _ref3.check_out;
        return [
          {
            dataIndex: "total",
            title: "Check In Total",
            valueType: "digit",
            colProps: {
              md: 12,
            },
            fieldProps: {
              style: {
                width: "100%",
              },
              disabled: true,
              value:
                rate && check_out && check_in
                  ? moment(check_out).diff(moment(check_in), "days") * rate
                  : 0,
            },
          },
        ];
      },
    },
    {
      title: "Remarks",
      dataIndex: "remarks",
      valueType: "textarea",
      hideInTable: true,
    },
  ],
};
