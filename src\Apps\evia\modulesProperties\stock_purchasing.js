import React from "react";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message } from "antd";
import PouchDb from "pouchdb-browser";
import StockPurchase from "../CustomViews/StockPurchase";

export default {
  CustomView: (data) => (
    <StockPurchase {...data} documentTitle="Event Invoice" />
  ),
  buffResults: async (results, collection) => {
    const receiptsDB = new PouchDb("stock_payments");
    const receiptsDocs = (
      await receiptsDB.allDocs({ include_docs: true, binary: true })
    ).rows
      .filter(r => r && r.doc && !r.doc._id.startsWith('_'))
      .map((r) => r.doc);

    return [
      ...results.map((doc) => {
        return {
          ...doc,
          receipts: (receiptsDocs.length > 0
            ? receiptsDocs.filter((d) => d && d.order && doc._id === d.order._id)
            : []
          ).reduce((previous, current) => {
            const amount = current && current.amount ? parseFloat(current.amount) : 0;
            return previous + amount;
          }, 0),
        };
      }),
    ];
  },
  MoreActions: (props) => {
    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;
    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Make Payment",
            name: (
              <>
                <BetaSchemaForm
                  submitter={{
                    searchConfig: {
                      resetText: "Close",
                      submitText: "Save",
                    },
                  }}
                  modalProps={{ centered: true }}
                  grid={true}
                  trigger={
                    <a key="button" type="primary">
                      Make Payment
                    </a>
                  }
                  title={"Receipt"}
                  destroyOnClose={true}
                  layoutType="ModalForm"
                  initialValues={{
                    order: {
                      label: `${record.supplier && record.supplier.label} - ${record._id
                        }`,
                      ...record,
                    },
                  }}
                  onFinish={async (values) => {
                    if (record) {
                      return pouchDatabase(
                        modules.stock_payments.collection,
                        databasePrefix
                      )
                        .saveDocument({ ...values }, CRUD_USER)
                        .then(() => {
                          message.success(
                            `${record.supplier && record.supplier.label
                            } Checked in.`
                          );

                          return true;
                        });
                    }
                  }}
                  columns={[...modules.stock_payments.columns]}
                />
              </>
            ),
          },
        ]}
      ></TableDropdown>
    );
  },
};
