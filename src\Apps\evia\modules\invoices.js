import { formatMoney } from "../../../Utils/functions";
import checkins from "./checkins";
import guests from "./guests";

var moment = require("moment");

export default {
  name: "General Invoices",
  icon: "PoundOutlined",
  path: "/invoices",
  collection: "invoices",
  singular: "Invoice",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      noBackDate: true,
      isPrintable: true,
    },
    {
      type: "dbSelect",
      dataIndex: "guest",
      collection: guests.collection,
      title: "Guest",
      label: ["title", "sur_name", "first_name"],

      isPrintable: true,
    },

    {
      title: "Items",
      valueType: "formList",
      width: "lg",
      colProps: {
        md: 24,
      },
      hideInTable: true,
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      dataIndex: "items",
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              title: "Item Description",
              dataIndex: "item",
              valueType: "text",
              colProps: {
                md: 8,
              },
            },
            {
              title: "Qty",
              dataIndex: "quantity",
              valueType: "digit",
              colProps: {
                md: 4,
              },
            },
            {
              title: "Unit Cost",
              colProps: {
                md: 6,
              },
              dataIndex: "cost",
              valueType: "digit",
            },
            {
              valueType: "dependency",
              colProps: {
                md: 6,
              },
              fieldProps: {
                name: ["cost", "quantity"],
              },
              columns: function columns(_ref2) {
                var cost = _ref2.cost,
                  quantity = _ref2.quantity;
                return [
                  {
                    title: "Total",
                    dataIndex: "total",
                    colProps: {
                      md: 6,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (cost ? cost : 0) * (quantity ? quantity : 0),
                    },
                    valueType: "digit",
                  },
                ];
              },
            },
          ],
        },
      ],
    },

    // {
    //   valueType: 'dependency',
    //   hideInTable: true,
    //   colProps: {
    //     md: 4
    //   },
    //   fieldProps: {
    //     name: ['reservation']
    //   },

    //   columns: function columns(_ref) {
    //     var reservation = _ref.reservation;

    //     if (reservation) {
    //       var _ref3;

    //       return [(_ref3 = {
    //         title: 'Items',
    //         valueType: 'formList',
    //         width: 'lg',
    //         colProps: {
    //           md: 24
    //         },
    //         hideInTable: true,
    //         fieldProps: {
    //           initialValue: [{
    //             item: "Rental Charges " + reservation.value,
    //             quantity: moment(reservation.departure_date).diff(moment(reservation.arrival_date), 'days'),
    //             cost: reservation.room_rate
    //           }],
    //           creatorButtonProps: {
    //             block: true,
    //             style: {
    //               width: '100%'
    //             },
    //             copyIconProps: false,
    //             creatorButtonText: 'Add Item'
    //           }
    //         },
    //         dataIndex: 'items'
    //       }, _ref3["hideInTable"] = true, _ref3.columns = [{
    //         valueType: 'group',
    //         colProps: {
    //           md: 24
    //         },
    //         columns: [{
    //           title: 'Item Description',
    //           dataIndex: 'item',
    //           valueType: "text",
    //           colProps: {
    //             md: 8
    //           }
    //         }, {
    //           title: 'Qty',
    //           dataIndex: 'quantity',
    //           valueType: "digit",
    //           colProps: {
    //             md: 4
    //           }
    //         }, {
    //           title: 'Unit Cost',
    //           colProps: {
    //             md: 4
    //           },
    //           dataIndex: 'cost',
    //           valueType: "digit"
    //         }, {
    //           title: 'Vat(%)',
    //           dataIndex: 'tax',
    //           initialValue: 18,
    //           colProps: {
    //             md: 4
    //           },
    //           fieldProps: {
    //             disabled: true
    //           },
    //           valueType: {
    //             type: 'percent',
    //             showSymbol: true
    //           }
    //         }, {
    //           valueType: 'dependency',
    //           colProps: {
    //             md: 4
    //           },
    //           fieldProps: {
    //             name: ['cost', 'quantity', 'tax']
    //           },
    //           columns: function columns(_ref2) {
    //             var cost = _ref2.cost,
    //                 quantity = _ref2.quantity,
    //                 tax = _ref2.tax;
    //             return [{
    //               title: 'Total',
    //               dataIndex: 'total',
    //               colProps: {
    //                 md: 4
    //               },
    //               fieldProps: {
    //                 disabled: true,
    //                 customSymble: 'UGX',
    //                 suffix: "SHS",
    //                 value: (tax ? tax : 0) / 100 * ((cost ? cost : 0) * (quantity ? quantity : 0)) + (cost ? cost : 0) * (quantity ? quantity : 0)
    //               },
    //               valueType: 'digit'
    //             }];
    //           }
    //         }]
    //       }], _ref3)];
    //     } else {
    //       return [];
    //     }
    //   }
    // },
    {
      valueType: "dependency",
      title: "Total",
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 24,
      },
      // hideInTable: true,
      render: function render(record, _ref4) {
        var items = _ref4.items;
        var total = 0;
        items &&
          items.map(function (item) {
            return (total = item
              ? (item.cost ? item.cost : 0) *
                (item.quantity ? item.quantity : 0)
              : total);
          });
        return total;
      },
      fieldProps: {
        name: ["items"],
        gap: 10, // value: items && items.map(item => total = item ? total + ((((item.tax ? item.tax : 0) / 100)) * ((item.cost ? item.cost : 0) * (item.quantity ? item.quantity : 0))) + ((item.cost ? item.cost : 0) * (item.quantity ? item.quantity : 0)) : total)
      },
      columns: function columns(_ref5) {
        var items = _ref5.items;
        var total = 0;
        items &&
          items.map(function (item) {
            total = item
              ? total +
                (item.cost ? item.cost : 0) *
                  (item.quantity ? item.quantity : 0)
              : total;
          });
        return [
          {
            dataIndex: "total",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return formatMoney(v);
            },
            renderFormItem: function renderFormItem() {
              return "Total : " + total;
            },
          },
        ];
      },
    },
    {
      valueType: "textarea",
      dataIndex: "description",
      hideInTable: true,
      title: "Description, Remarks or Notes",
    },
  ],
};
