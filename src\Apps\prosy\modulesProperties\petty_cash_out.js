import React from "react";
import { message } from "antd";
import PettyCashOut from "../CustomViews/PettyCashOut";

export default {
  CustomView: (data) => <PettyCashOut {...data} />,
  print: true,
  // After saving a petty cash out record, create an expense record and update the petty cash out with expense ID
  afterSave: async (data, pouchDatabase, databasePrefix) => {
    // Get CRUD_USER from localStorage since it's not passed in this calling convention
    const CRUD_USER = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER") || '{}');

    // Extract the actual document from the data structure
    // The data can come in different formats: { id, doc } or just the doc directly
    const doc = data.doc || data;
    const docId = data.id || data._id;

    console.log("AfterSave called with:", {
      dataId: docId,
      hasDatabase: typeof pouchDatabase === 'function',
      databasePrefix,
      crudUser: CRUD_USER?.first_name || 'Unknown'
    });

    try {
      // Only process new records (first revision) to avoid duplicate expense creation
      if (doc._rev && !doc._rev.startsWith("1-")) {
        console.log("Skipping afterSave - not a new record (revision:", doc._rev, ")");
        return;
      }

      // Check if we already have an expense_id - if so, we'll update the existing expense
      let existingExpense = null;
      let isUpdatingExpense = false;

      if (doc.expense_id) {
        console.log("Expense ID already exists, will update existing expense:", doc.expense_id);
        try {
          existingExpense = await pouchDatabase("expenses", databasePrefix).getDocument(doc.expense_id);
          isUpdatingExpense = true;
          console.log("Found existing expense record to update");
        } catch (error) {
          console.warn("Expense ID exists but expense record not found, will create new expense:", error.message);
          // Continue to create new expense if the referenced expense doesn't exist
        }
      }

      console.log(isUpdatingExpense ? "Updating existing expense record" : "Creating new expense record", "from petty cash out:", docId);

      // Validate required data
      if (!doc.expense_date) {
        console.error("Missing expense_date - received:", doc.expense_date);
        console.error("All doc fields:", Object.keys(doc));
        message.error("Missing expense date - cannot create expense record");
        return;
      }

      if (!doc.supplier) {
        console.error("Missing supplier");
        message.error("Missing supplier - cannot create expense record");
        return;
      }

      if (!doc.sub_expenses || doc.sub_expenses.length === 0) {
        console.error("Missing sub_expenses");
        message.error("Missing sub expenses - cannot create expense record");
        return;
      }

      // Calculate total amount from sub_expenses
      const totalAmount = doc.sub_expenses?.reduce((sum, expense) => {
        return sum + (parseFloat(expense.amount) || 0);
      }, 0) || 0;

      if (totalAmount <= 0) {
        console.error("Total amount is zero or negative:", totalAmount);
        message.error("Total amount must be greater than zero");
        return;
      }

      // Prepare expense record data
      const expenseData = {
        // If updating, preserve the existing document structure
        ...(isUpdatingExpense ? existingExpense : {}),
        // Update with current petty cash out data
        expense_Date: doc.expense_date,
        supplier: doc.supplier, // This is already a dbSelect object with value and label
        expense_type: "company", // Default to company expense
        expense_category: null, // Will be set from first sub expense if available
        method_of_payment: doc.payment_method === "mobile_money" ? "MTN Mobile Money" :
                          doc.payment_method === "bank" ? "Bank Deposit" :
                          doc.payment_method === "cheque" ? "Cheque" :
                          doc.payment_method === "cash" ? "Cash" : "Cash",
        description: `Petty Cash Out - ${doc.particulars}${doc.ref_no ? ` (Ref: ${doc.ref_no})` : ''}`,
        amount: totalAmount,
        property: null, // Will be set from first sub expense if available
        unit: null,
        // Add required payment voucher number (using petty cash out ID as reference)
        payment_voucher_number: `PCO-${docId}`,
        // Add branch information
        branch: SELECTED_BRANCH,
        // Add reference to the petty cash out record
        petty_cash_out_id: docId,
        // Add sub expenses details as additional info
        sub_expenses_details: doc.sub_expenses?.map(expense => ({
          details: expense.details,
          property: expense.property,
          account: expense.account,
          expense_center: expense.expense_center,
          amount: expense.amount
        })) || []
      };

      console.log(`Prepared expense data structure (${isUpdatingExpense ? 'UPDATE' : 'CREATE'}):`, {
        expense_Date: expenseData.expense_Date,
        supplier: expenseData.supplier,
        expense_type: expenseData.expense_type,
        method_of_payment: expenseData.method_of_payment,
        amount: expenseData.amount,
        branch: expenseData.branch,
        payment_voucher_number: expenseData.payment_voucher_number,
        totalSubExpenses: doc.sub_expenses?.length || 0,
        existingExpenseId: isUpdatingExpense ? existingExpense._id : null
      });

      // Set expense category and property from first sub expense if available
      if (doc.sub_expenses && doc.sub_expenses.length > 0) {
        const firstSubExpense = doc.sub_expenses[0];
        if (firstSubExpense.expense_center) {
          expenseData.expense_category = firstSubExpense.expense_center;
        }
        if (firstSubExpense.property) {
          expenseData.property = firstSubExpense.property;
          expenseData.expense_type = "project"; // Change to project if property is specified
        }
      }

      // Save the expense record
      console.log(`Attempting to ${isUpdatingExpense ? 'update' : 'create'} expense data:`, expenseData);
      let expenseResult;
      try {
        expenseResult = await pouchDatabase("expenses", databasePrefix).saveDocument(expenseData, CRUD_USER);
      } catch (saveError) {
        console.error(`Error ${isUpdatingExpense ? 'updating' : 'creating'} expense document:`, saveError);
        console.error("Expense data that failed to save:", expenseData);
        console.error("Save error details:", {
          message: saveError.message,
          name: saveError.name,
          stack: saveError.stack
        });
        message.error(`Error ${isUpdatingExpense ? 'updating' : 'creating'} expense: ` + (saveError.message || "Unknown error"));
        return;
      }

      console.log("Expense save result:", expenseResult);

      // Handle different return formats from different database implementations
      // SimplifiedDB returns { id: docId, doc: preparedDoc }
      // Database returns { ...doc, _id: docId }
      const expenseId = expenseResult._id || expenseResult.id;

      if (expenseResult && expenseId) {
        console.log(`Expense record ${isUpdatingExpense ? 'updated' : 'created'} successfully:`, expenseId);

        // Update the petty cash out record with the expense ID (only if it's a new expense)
        if (!isUpdatingExpense) {
          const updatedPettyCashOut = {
            ...doc,
            expense_id: expenseId
          };

          try {
            const updateResult = await pouchDatabase("petty_cash_out", databasePrefix).saveDocument(updatedPettyCashOut, CRUD_USER);
            console.log("Petty cash out update result:", updateResult);

            console.log("Petty cash out record updated with expense ID:", expenseId);
            message.success(`Expense record ${expenseId} created and linked successfully`);
          } catch (updateError) {
            console.error("Error updating petty cash out record:", updateError);
            console.error("Update error details:", {
              message: updateError.message,
              name: updateError.name,
              docId: docId,
              expenseId: expenseId
            });
            message.warning(`Expense record ${expenseId} created but failed to update petty cash out record: ${updateError.message || "Unknown error"}`);
          }
        } else {
          // For updates, just show success message
          message.success(`Expense record ${expenseId} updated successfully`);
        }
      } else {
        console.error(`Failed to ${isUpdatingExpense ? 'update' : 'create'} expense record - no ID returned`);
        console.error("Expense result:", expenseResult);
        message.error(`Failed to ${isUpdatingExpense ? 'update' : 'create'} expense record - no ID returned`);
      }

    } catch (error) {
      console.error("Error in petty cash out afterSave:", error);
      console.error("Error stack:", error.stack);
      console.error("Error details:", {
        message: error.message,
        name: error.name,
        docId: docId,
        hasExpenseId: !!doc.expense_id
      });
      message.error("Error processing expense record: " + (error.message || "Unknown error"));
    }
  }
};
