import React from "react";
import OrderInvoice from "../CustomViews/OrderInvoice";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message } from "antd";
import moment from "moment";
import AppDatabase from "../../../Utils/AppDatabase";
import { refreshRecordData } from "../../../Utils/RecordRefreshUtility";

export default {
  afterSave: async (data) => {
    if (data.fullyPaid) {
      const invoiceAmount = data.items.reduce(
        (a, b) => a + b.cost * b.quantity,
        0
      ); // Calculate invoice amount



      const receptData = {
        order: {
          label: data.guest.label + " - " + data._id,
          value: data._id,
          ...data,
        },
        amount: invoiceAmount,
        date: moment().toISOString(),
        method_of_payment: "Cash",
        description: "Auto Generated Receipt",
        _id: Date.now().toString(36).toUpperCase(),
        branch: data.branch,
      };
      const databasePrefix = localStorage.getItem("DB_PREFIX") || "";
      const receiptsDB = AppDatabase("order_receipts", databasePrefix);
      receiptsDB.save(receptData).then(() => {
        message.success("Receipt Also Added");
      });

    }

    // await receiptsDB.put(receiptsDoc);
  },
  CustomView: (data) => (
    <OrderInvoice {...data} documentTitle="Order Invoice" />
  ),
  buffResults: async (results, pouchDatabase, databasePrefix) => {
    const receiptsDB = AppDatabase("order_receipts", databasePrefix);
    const receiptsDocs = await receiptsDB.getAllData();

    return [
      ...results.map((doc) => {
        return {
          ...doc,
          receipts: (receiptsDocs.length > 0
            ? receiptsDocs.filter((d) => d.order && doc._id === d.order._id)
            : []
          ).reduce((previous, current) => {
            return previous + parseInt(current.amount);
          }, 0),
        };
      }),
    ];
  },
  MoreActions: (props) => {

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
      actionRef,
      updateOptimisticRecord,
    } = props;

    const refreshRecord = async (updatedRecord) => {
      return await refreshRecordData(updatedRecord, updateOptimisticRecord, pouchDatabase, collection, databasePrefix);
    };

    const orderBalance =
      (record.items
        ? record.items.reduce(
            (total, item) =>
              total +
              (item.cost ? item.cost : 0) * (item.quantity ? item.quantity : 0),
            0
          )
        : 0) - (record && record.receipts ? record.receipts : 0);

    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH")
      ? localStorage.getItem("SELECTED_BRANCH")
      : "none";

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const canCreateReceipts =
      APP_USER_PERMISSIONS.root ||
      APP_USER_PERMISSIONS.permissions.find(
        (p) => p.module === "order_receipts"
      ).create;



    return (
      canCreateReceipts &&
      orderBalance > 0 && (
        <TableDropdown
          key="actionGroup"
          menus={[
            {
              key: "Receipt Order",
              name: (
                <>
                  <BetaSchemaForm
                    submitter={{
                      searchConfig: {
                        resetText: "Close",
                        submitText: "Save",
                      },
                    }}
                    modalProps={{ centered: true }}
                    grid={true}
                    trigger={
                      <a key="button" type="primary">
                        Receipt Order
                      </a>
                    }
                    title={"Receipt"}
                    destroyOnClose={true}
                    layoutType="ModalForm"
                    initialValues={{
                      order: {
                        label:
                          record &&
                          record.guest &&
                          record.guest.label + " - " + record._id,
                        ...record,
                      },
                      date: moment().startOf("day"),
                    }}
                    // shouldUpdate={true}
                    onFinish={async (values) => {
                      if (record) {
                        return pouchDatabase(
                          modules.order_receipts.collection,
                          databasePrefix
                        )
                          .saveDocument(
                            {
                              ...values,
                              entrant: CRUD_USER,
                              branch: SELECTED_BRANCH,
                            },
                            CRUD_USER
                          )
                          .then(async () => {
                            await refreshRecord(record);
                            message.success(
                              `${
                                record.guest && record.guest.label
                              } has Receipted.`
                            );
                            actionRef.current.reloadAndRest();
                            return true;
                          });
                        return true;
                      }
                    }}
                    columns={[...modules.order_receipts.columns]}
                  />
                </>
              ),
            },
          ]}
        ></TableDropdown>
      )
    );
  },
};
